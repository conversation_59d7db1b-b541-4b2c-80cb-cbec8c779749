<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects/' . $project['id'] . '/officers') ?>" class="btn btn-outline-secondary">
    <i class="bi bi-arrow-left me-2"></i>
    Back to Officers
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 fw-bold text-primary mb-2">
            <i class="bi bi-person-plus me-2"></i>
            Assign Project Officer
        </h1>
        <p class="text-muted mb-0">
            Assign a new officer to project: <strong><?= esc($project['title']) ?></strong>
        </p>
    </div>
</div>

<!-- Project Context Card -->
<div class="card mb-4">
    <div class="card-header bg-light">
        <div class="d-flex align-items-center gap-3">
            <div class="rounded-circle d-flex align-items-center justify-content-center text-white fw-bold"
                 style="width: 40px; height: 40px; background: var(--promis-gradient-primary);">
                <i class="bi bi-folder"></i>
            </div>
            <div>
                <h5 class="fw-semibold text-primary mb-1">
                    <?= esc($project['title']) ?>
                </h5>
                <div class="text-muted small">
                    Project Code: <?= esc($project['pro_code']) ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Current Officers Summary -->
<?php if (!empty($currentOfficers)): ?>
<div class="card mb-4">
    <div class="card-header bg-light">
        <h5 class="card-title mb-0">
            <i class="bi bi-people me-2"></i>
            Currently Assigned Officers
        </h5>
    </div>

    <div class="card-body">
        <div class="vstack gap-3">
            <?php foreach ($currentOfficers as $officer): ?>
                <div style="background: var(--bg-tertiary); border-radius: var(--radius-md); padding: var(--spacing-md); display: flex; align-items: center; gap: var(--spacing-md);">
                    <div style="flex: 1;">
                        <div style="font-weight: 600; color: var(--text-primary);">
                            <?= esc($officer['name']) ?>
                        </div>
                        <div style="font-size: 0.875rem; color: var(--text-muted);">
                            <?= esc($officer['username']) ?> • <?= esc($officer['email']) ?>
                        </div>
                    </div>
                    <span style="background: <?= $officer['role'] === 'lead' ? 'var(--brand-success)' : ($officer['role'] === 'certifier' ? 'var(--brand-secondary)' : 'var(--brand-accent)') ?>; color: white; padding: var(--spacing-xs) var(--spacing-sm); border-radius: var(--radius-sm); font-size: 0.75rem; font-weight: 600; text-transform: uppercase;">
                        <?= esc($officer['role']) ?>
                    </span>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Assignment Form -->
<div class="card mb-xl">
    <div class="card-header">
        ➕ Assign New Officer
    </div>
    
    <div style="padding: var(--spacing-xl);">
        <?php if (!empty($availableOfficers)): ?>
            <form method="POST" action="<?= base_url('admin/projects/' . $project['id'] . '/officers/create') ?>">
                <?= csrf_field() ?>
                <!-- Officer Selection -->
                <div style="margin-bottom: var(--spacing-lg);">
                    <label style="display: block; color: var(--text-primary); font-weight: 600; margin-bottom: var(--spacing-sm);">
                        Select Officer <span style="color: var(--brand-danger);">*</span>
                    </label>
                    <select name="user_id" required style="width: 100%; padding: var(--spacing-md); border: 2px solid var(--brand-danger); border-radius: var(--radius-md); font-family: inherit; background: white;">
                        <option value="">Choose an officer...</option>
                        <?php foreach ($availableOfficers as $officer): ?>
                            <option value="<?= $officer['id'] ?>" <?= old('user_id') == $officer['id'] ? 'selected' : '' ?>>
                                <?= esc($officer['name']) ?> (<?= esc($officer['username']) ?>) - <?= esc($officer['email']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <div style="font-size: 0.875rem; color: var(--text-muted); margin-top: var(--spacing-xs);">
                        Only users with Project Officer flag are shown
                    </div>
                </div>

                <!-- Role Selection -->
                <div style="margin-bottom: var(--spacing-lg);">
                    <label style="display: block; color: var(--text-primary); font-weight: 600; margin-bottom: var(--spacing-sm);">
                        Officer Role <span style="color: var(--brand-danger);">*</span>
                    </label>
                    
                    <div style="display: grid; gap: var(--spacing-md);">
                        <!-- Lead Role -->
                        <div style="border: 2px solid var(--border-color); border-radius: var(--radius-md); padding: var(--spacing-lg); position: relative;">
                            <label style="display: flex; align-items: start; gap: var(--spacing-md); cursor: pointer;">
                                <input type="radio" name="role" value="lead" <?= old('role') == 'lead' ? 'checked' : '' ?> <?= !empty($currentOfficers) && array_filter($currentOfficers, fn($o) => $o['role'] === 'lead') ? 'disabled' : '' ?> style="margin-top: var(--spacing-xs);">
                                <div style="flex: 1;">
                                    <div style="font-weight: 600; color: var(--text-primary); margin-bottom: var(--spacing-xs);">
                                        👑 Lead Officer
                                    </div>
                                    <div style="font-size: 0.875rem; color: var(--text-secondary); line-height: 1.5;">
                                        Primary project supervisor with full authority and responsibility. Only one lead per project.
                                    </div>
                                    <?php if (!empty($currentOfficers) && array_filter($currentOfficers, fn($o) => $o['role'] === 'lead')): ?>
                                        <div style="font-size: 0.75rem; color: var(--brand-warning); margin-top: var(--spacing-xs); font-weight: 600;">
                                            ⚠️ Lead already assigned to this project
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </label>
                        </div>

                        <!-- Certifier Role -->
                        <div style="border: 2px solid var(--border-color); border-radius: var(--radius-md); padding: var(--spacing-lg);">
                            <label style="display: flex; align-items: start; gap: var(--spacing-md); cursor: pointer;">
                                <input type="radio" name="role" value="certifier" <?= old('role') == 'certifier' ? 'checked' : '' ?> style="margin-top: var(--spacing-xs);">
                                <div style="flex: 1;">
                                    <div style="font-weight: 600; color: var(--text-primary); margin-bottom: var(--spacing-xs);">
                                        ✅ Certifier
                                    </div>
                                    <div style="font-size: 0.875rem; color: var(--text-secondary); line-height: 1.5;">
                                        Responsible for quality assurance, milestone verification, and project certification.
                                    </div>
                                </div>
                            </label>
                        </div>

                        <!-- Support Role -->
                        <div style="border: 2px solid var(--border-color); border-radius: var(--radius-md); padding: var(--spacing-lg);">
                            <label style="display: flex; align-items: start; gap: var(--spacing-md); cursor: pointer;">
                                <input type="radio" name="role" value="support" <?= old('role') == 'support' ? 'checked' : '' ?> style="margin-top: var(--spacing-xs);">
                                <div style="flex: 1;">
                                    <div style="font-weight: 600; color: var(--text-primary); margin-bottom: var(--spacing-xs);">
                                        🤝 Support Officer
                                    </div>
                                    <div style="font-size: 0.875rem; color: var(--text-secondary); line-height: 1.5;">
                                        Provides assistance and support to the project team with specific tasks and coordination.
                                    </div>
                                </div>
                            </label>
                        </div>
                    </div>

                    <?php if (empty($currentOfficers)): ?>
                        <div style="background: var(--bg-accent); border: 1px solid var(--brand-primary); border-radius: var(--radius-md); padding: var(--spacing-md); margin-top: var(--spacing-md);">
                            <div style="font-size: 0.875rem; color: var(--brand-primary); font-weight: 600;">
                                ℹ️ First Officer Assignment
                            </div>
                            <div style="font-size: 0.875rem; color: var(--text-secondary); margin-top: var(--spacing-xs);">
                                This is the first officer being assigned to this project. They will automatically become the Lead Officer regardless of role selection.
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Form Actions -->
                <div style="display: flex; gap: var(--spacing-md); justify-content: flex-end; padding-top: var(--spacing-lg); border-top: 1px solid var(--border-color);">
                    <a href="<?= base_url('admin/projects/' . $project['id'] . '/officers') ?>" class="btn btn-secondary">
                        Cancel
                    </a>
                    <button type="submit" class="btn btn-primary">
                        ➕ Assign Officer
                    </button>
                </div>
            </form>
        <?php else: ?>
            <div style="text-align: center; padding: var(--spacing-xl); color: var(--text-muted);">
                <div style="font-size: 3rem; margin-bottom: var(--spacing-md);">👥</div>
                <h3 style="color: var(--text-secondary); margin-bottom: var(--spacing-sm);">No Available Officers</h3>
                <p style="margin-bottom: var(--spacing-lg);">
                    All project officers in your organization are already assigned to this project, or there are no users with the Project Officer flag.
                </p>
                <div style="display: flex; gap: var(--spacing-md); justify-content: center;">
                    <a href="<?= base_url('admin/users') ?>" class="btn btn-secondary">
                        👥 Manage Users
                    </a>
                    <a href="<?= base_url('admin/projects/' . $project['id'] . '/officers') ?>" class="btn btn-primary">
                        ← Back to Officers
                    </a>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
// Auto-select lead role if no current officers
<?php if (empty($currentOfficers)): ?>
document.addEventListener('DOMContentLoaded', function() {
    const leadRadio = document.querySelector('input[name="role"][value="lead"]');
    if (leadRadio && !leadRadio.disabled) {
        leadRadio.checked = true;
    }
});
<?php endif; ?>

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const userId = document.querySelector('select[name="user_id"]').value;
    const role = document.querySelector('input[name="role"]:checked');
    
    if (!userId) {
        e.preventDefault();
        alert('Please select an officer to assign.');
        return;
    }
    
    if (!role) {
        e.preventDefault();
        alert('Please select a role for the officer.');
        return;
    }
});
</script>

<?= $this->endSection() ?>
