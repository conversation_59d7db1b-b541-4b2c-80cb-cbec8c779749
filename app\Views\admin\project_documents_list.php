<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects/' . $project['id']) ?>#project-documents" class="btn btn-outline-secondary">
    <i class="bi bi-arrow-left me-2"></i>
    Back to Project Profile
</a>
<a href="<?= base_url('admin/projects/' . $project['id'] . '/documents/create') ?>" class="btn btn-primary promis-btn-gradient">
    <i class="bi bi-file-earmark-plus me-2"></i>
    Upload Document
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 fw-bold text-primary mb-2">
            <i class="bi bi-folder me-2"></i>
            Project Documents
        </h1>
        <p class="text-muted mb-0">
            Document repository for project: <strong><?= esc($project['title']) ?></strong>
        </p>
    </div>
</div>

<!-- Document Statistics -->
<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--spacing-lg); margin-bottom: var(--spacing-xl);">
    <!-- Total Documents -->
    <div class="card" style="text-align: center; padding: var(--spacing-lg);">
        <div style="font-size: 2rem; margin-bottom: var(--spacing-sm);">📁</div>
        <div style="font-size: 1.5rem; font-weight: 700; color: var(--brand-primary); margin-bottom: var(--spacing-xs);">
            <?= $documentStats['total_documents'] ?? 0 ?>
        </div>
        <div style="color: var(--text-muted); font-size: 0.875rem;">Total Documents</div>
    </div>

    <!-- Recent Uploads -->
    <div class="card" style="text-align: center; padding: var(--spacing-lg);">
        <div style="font-size: 2rem; margin-bottom: var(--spacing-sm);">📤</div>
        <div style="font-size: 1.5rem; font-weight: 700; color: var(--brand-success); margin-bottom: var(--spacing-xs);">
            <?= $documentStats['recent_uploads'] ?? 0 ?>
        </div>
        <div style="color: var(--text-muted); font-size: 0.875rem;">Recent Uploads</div>
    </div>

    <!-- Document Types -->
    <div class="card" style="text-align: center; padding: var(--spacing-lg);">
        <div style="font-size: 2rem; margin-bottom: var(--spacing-sm);">🏷️</div>
        <div style="font-size: 1.5rem; font-weight: 700; color: var(--brand-secondary); margin-bottom: var(--spacing-xs);">
            <?= count($documentStats['by_type'] ?? []) ?>
        </div>
        <div style="color: var(--text-muted); font-size: 0.875rem;">Document Types</div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-xl">
    <div class="card-header">
        🔍 Filter Documents
    </div>
    <div style="padding: var(--spacing-xl);">
        <form method="GET" action="<?= base_url('admin/projects/' . $project['id'] . '/documents') ?>">
            <div style="display: grid; grid-template-columns: 1fr 2fr auto; gap: var(--spacing-lg); align-items: end;">
                <div>
                    <label style="display: block; margin-bottom: var(--spacing-xs); font-weight: 600; color: var(--text-primary);">Category</label>
                    <select name="category" id="category" style="width: 100%; padding: var(--spacing-sm); border: 1px solid var(--border-color); border-radius: var(--radius-sm);">
                        <option value="">All Categories</option>
                        <?php foreach ($categories as $key => $label): ?>
                            <option value="<?= $key ?>" <?= ($filters['category'] == $key) ? 'selected' : '' ?>>
                                <?= esc($label) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div>
                    <label style="display: block; margin-bottom: var(--spacing-xs); font-weight: 600; color: var(--text-primary);">Search</label>
                    <input type="text" name="search" id="search"
                           style="width: 100%; padding: var(--spacing-sm); border: 1px solid var(--border-color); border-radius: var(--radius-sm);"
                           placeholder="Search by description, path, or type..."
                           value="<?= esc($filters['search'] ?? '') ?>">
                </div>
                <div style="display: flex; gap: var(--spacing-sm);">
                    <button type="submit" class="btn btn-primary" style="padding: var(--spacing-sm) var(--spacing-md);">Filter</button>
                    <a href="<?= base_url('admin/projects/' . $project['id'] . '/documents') ?>" class="btn btn-secondary" style="padding: var(--spacing-sm) var(--spacing-md);">Clear</a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Documents List -->
<div class="card">
    <div class="card-header">
        📁 Project Documents
    </div>

    <?php if (!empty($documents)): ?>
        <div style="overflow-x: auto;">
            <table style="width: 100%; border-collapse: collapse;">
                <thead>
                    <tr style="background: var(--bg-tertiary); border-bottom: 1px solid var(--border-color);">
                        <th style="padding: var(--spacing-md); text-align: left; font-weight: 600; color: var(--text-primary);">
                            Description
                        </th>
                        <th style="padding: var(--spacing-md); text-align: center; font-weight: 600; color: var(--text-primary);">
                            Category
                        </th>
                        <th style="padding: var(--spacing-md); text-align: left; font-weight: 600; color: var(--text-primary);">
                            File Path
                        </th>
                        <th style="padding: var(--spacing-md); text-align: center; font-weight: 600; color: var(--text-primary);">
                            Version
                        </th>
                        <th style="padding: var(--spacing-md); text-align: center; font-weight: 600; color: var(--text-primary);">
                            Upload Date
                        </th>
                        <th style="padding: var(--spacing-md); text-align: center; font-weight: 600; color: var(--text-primary);">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($documents as $document): ?>
                        <tr style="border-bottom: 1px solid var(--border-color);">
                            <td style="padding: var(--spacing-md); vertical-align: top;">
                                <?php if (!empty($document['description'])): ?>
                                    <div style="font-weight: 600; color: var(--text-primary); margin-bottom: var(--spacing-xs);">
                                        <?= esc($document['description']) ?>
                                    </div>
                                <?php else: ?>
                                    <div style="font-style: italic; color: var(--text-muted);">No description</div>
                                <?php endif; ?>
                                <div style="font-size: 0.75rem; color: var(--text-muted);">
                                    Created: <?= date('M j, Y', strtotime($document['created_at'])) ?>
                                </div>
                            </td>
                            <td style="padding: var(--spacing-md); text-align: center; vertical-align: top;">
                                <?php if (!empty($document['doc_type'])): ?>
                                    <span style="background: var(--brand-secondary); color: white; padding: var(--spacing-xs) var(--spacing-sm); border-radius: var(--radius-sm); font-size: 0.75rem; font-weight: 600;">
                                        <?= esc($categories[$document['doc_type']] ?? $document['doc_type']) ?>
                                    </span>
                                <?php else: ?>
                                    <span style="background: var(--text-muted); color: white; padding: var(--spacing-xs) var(--spacing-sm); border-radius: var(--radius-sm); font-size: 0.75rem;">
                                        Unspecified
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td style="padding: var(--spacing-md); vertical-align: top;">
                                <div style="font-weight: 500; color: var(--text-primary);">
                                    📄 <?= esc(basename($document['doc_path'])) ?>
                                </div>
                            </td>
                            <td style="padding: var(--spacing-md); text-align: center; vertical-align: top;">
                                <span style="background: var(--brand-accent); color: white; padding: var(--spacing-xs) var(--spacing-sm); border-radius: var(--radius-sm); font-size: 0.75rem; font-weight: 600;">
                                    v<?= $document['version_no'] ?? 1 ?>
                                </span>
                            </td>
                            <td style="padding: var(--spacing-md); text-align: center; vertical-align: top;">
                                <div style="font-weight: 500; color: var(--text-primary);">
                                    <?= date('M j, Y', strtotime($document['created_at'])) ?>
                                </div>
                                <div style="font-size: 0.75rem; color: var(--text-muted);">
                                    <?= date('g:i A', strtotime($document['created_at'])) ?>
                                </div>
                            </td>
                            <td style="padding: var(--spacing-md); text-align: center; vertical-align: top;">
                                <div style="display: flex; gap: var(--spacing-xs); justify-content: center;">
                                    <a href="<?= base_url('admin/projects/' . $project['id'] . '/documents/' . $document['id'] . '/download') ?>"
                                       class="btn btn-primary" style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;">
                                        📥 Download
                                    </a>
                                    <a href="<?= base_url('admin/projects/' . $project['id'] . '/documents/' . $document['id'] . '/edit') ?>"
                                       class="btn btn-secondary" style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;">
                                        ✏️ Edit
                                    </a>
                                    <button onclick="showDeleteModal(<?= $document['id'] ?>, '<?= esc($document['description'] ?? 'Document') ?>')"
                                            class="btn btn-danger" style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;">
                                        🗑️ Delete
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <div style="text-align: center; padding: var(--spacing-xl); color: var(--text-muted);">
            <div style="font-size: 3rem; margin-bottom: var(--spacing-md);">📁</div>
            <h3 style="color: var(--text-secondary); margin-bottom: var(--spacing-sm);">No Documents Found</h3>
            <p style="margin-bottom: var(--spacing-lg);">Upload your first project document to get started.</p>
            <a href="<?= base_url('admin/projects/' . $project['id'] . '/documents/create') ?>" class="btn btn-primary">
                📁 Upload First Document
            </a>
        </div>
    <?php endif; ?>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.5); z-index: 10000; align-items: center; justify-content: center;">
    <div style="background: var(--bg-primary); border-radius: var(--radius-lg); padding: var(--spacing-xl); max-width: 400px; width: 90%; box-shadow: var(--shadow-xl);">
        <h3 style="color: var(--text-primary); margin-bottom: var(--spacing-md);">Delete Project Document</h3>
        <p style="color: var(--text-secondary); margin-bottom: var(--spacing-lg);">
            Are you sure you want to delete the document "<span id="deleteDocumentText" style="font-weight: 600;"></span>"? This action cannot be undone and will permanently delete the file from the server.
        </p>

        <div class="d-flex gap-md justify-content-between">
            <button onclick="hideDeleteModal()" class="btn btn-secondary">Cancel</button>
            <button onclick="confirmDelete()" class="btn btn-danger">Delete Document</button>
        </div>
    </div>
</div>

<script>
let currentDeleteDocumentId = null;

function showDeleteModal(documentId, documentDescription) {
    currentDeleteDocumentId = documentId;
    document.getElementById('deleteDocumentText').textContent = documentDescription || 'this document';
    document.getElementById('deleteModal').style.display = 'flex';
}

function hideDeleteModal() {
    document.getElementById('deleteModal').style.display = 'none';
    currentDeleteDocumentId = null;
}

function confirmDelete() {
    if (currentDeleteDocumentId) {
        // Create and submit form
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '<?= base_url('admin/projects/' . $project['id'] . '/documents/') ?>' + currentDeleteDocumentId + '/delete';

        // Add CSRF token if available
        <?php if (csrf_token()): ?>
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = '<?= csrf_token() ?>';
        csrfInput.value = '<?= csrf_hash() ?>';
        form.appendChild(csrfInput);
        <?php endif; ?>

        document.body.appendChild(form);
        form.submit();
    }
}

// Close modal when clicking outside
document.getElementById('deleteModal').addEventListener('click', function(e) {
    if (e.target === this) {
        hideDeleteModal();
    }
});
</script>

<style>
/* Mobile responsive adjustments */
@media (max-width: 768px) {
    .btn {
        padding: var(--spacing-sm) var(--spacing-md) !important;
        font-size: 0.875rem !important;
    }

    table {
        font-size: 0.875rem;
    }

    td, th {
        padding: var(--spacing-sm) !important;
    }
}

/* Form styling consistency */
select, input[type="text"] {
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    transition: border-color 0.2s ease;
}

select:focus, input[type="text"]:focus {
    outline: none;
    border-color: var(--brand-primary);
    box-shadow: 0 0 0 2px rgba(var(--brand-primary-rgb), 0.1);
}

/* Table hover effects */
tbody tr:hover {
    background: var(--bg-secondary) !important;
}

/* Color-coded Form Input System for Filters */
input[type="text"], select {
    border: 2px solid #10B981 !important; /* Green for optional */
    border-radius: var(--radius-sm);
    padding: var(--spacing-md);
    font-size: 0.95rem;
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: border-color 0.2s;
}
input[type="text"]:focus, select:focus {
    border-color: #059669 !important;
    box-shadow: 0 0 0 3px rgba(16,185,129,0.1);
}
input[type="text"]:hover, select:hover {
    border-color: #059669 !important;
}
input[type="text"][required], select[required] {
    border: 2px solid #EF4444 !important;
}
input[type="text"][required]:focus, select[required]:focus {
    border-color: #DC2626 !important;
    box-shadow: 0 0 0 3px rgba(239,68,68,0.1);
}
input[type="text"][required]:hover, select[required]:hover {
    border-color: #DC2626 !important;
}
</style>
<?= $this->endSection() ?>
