<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects/create') ?>" class="btn btn-primary promis-btn-gradient">
    <i class="bi bi-folder-plus me-2"></i>
    Create New Project
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 fw-bold text-primary mb-2">
            <i class="bi bi-kanban me-2"></i>
            Project Management
        </h1>
        <p class="text-muted mb-0">
            Manage projects for <strong><?= esc($admin_organization_name) ?></strong>
        </p>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-header bg-light">
        <h5 class="card-title mb-0">
            <i class="bi bi-funnel me-2"></i>
            Filters & Search
        </h5>
    </div>
    <div class="card-body">
        <form method="get" action="<?= base_url('admin/projects') ?>" class="d-flex gap-3 align-items-center flex-wrap">

            <!-- Search -->
            <div class="flex-grow-1" style="min-width: 200px;">
                <input type="text" name="search" class="form-control"
                       placeholder="Search projects..." value="<?= esc($filters['search']) ?>">
            </div>

            <!-- Status Filter -->
            <div style="min-width: 150px;">
                <select name="status" class="form-select">
                    <option value="">All Status</option>
                    <option value="planning" <?= ($filters['status'] === 'planning') ? 'selected' : '' ?>>Planning</option>
                    <option value="active" <?= ($filters['status'] === 'active') ? 'selected' : '' ?>>Active</option>
                    <option value="on-hold" <?= ($filters['status'] === 'on-hold') ? 'selected' : '' ?>>On Hold</option>
                    <option value="completed" <?= ($filters['status'] === 'completed') ? 'selected' : '' ?>>Completed</option>
                    <option value="cancelled" <?= ($filters['status'] === 'cancelled') ? 'selected' : '' ?>>Cancelled</option>
                </select>
            </div>

            <!-- Organization Context (Auto-filtered) -->
            <div class="d-flex align-items-center gap-2 bg-light border border-primary rounded p-2" style="min-width: 200px;">
                <div class="rounded-circle d-flex align-items-center justify-content-center text-white"
                     style="width: 30px; height: 30px; background: var(--promis-gradient-primary);">
                    <i class="bi bi-building"></i>
                </div>
                <div>
                    <div class="fw-semibold text-primary small">
                        <?= esc($admin_organization_name) ?>
                    </div>
                    <div class="text-muted" style="font-size: 0.75rem;">
                        Your Organization
                    </div>
                </div>
            </div>

            <!-- Filter Buttons -->
            <div class="d-flex gap-2">
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-funnel me-2"></i>
                    Filter
                </button>
                <a href="<?= base_url('admin/projects') ?>" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-clockwise me-2"></i>
                    Clear
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Projects Table -->
<div class="card">
    <div class="card-header bg-light">
        <h5 class="card-title mb-0">
            <i class="bi bi-kanban me-2"></i>
            Projects (<?= count($projects) ?> found)
        </h5>
    </div>

    <?php if (!empty($projects)): ?>
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>Project</th>
                        <th>Status</th>
                        <th>Location</th>
                        <th>Timeline</th>
                        <th>Created</th>
                        <th class="text-center">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($projects as $project): ?>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center gap-3">
                                    <div class="rounded-circle d-flex align-items-center justify-content-center text-white fw-bold"
                                         style="width: 40px; height: 40px; background: var(--promis-gradient-primary);">
                                        <i class="bi bi-folder"></i>
                                    </div>
                                    <div>
                                        <div class="fw-semibold text-primary mb-1">
                                            <?= esc($project['title']) ?>
                                        </div>
                                        <div class="text-muted small">
                                            <?= esc($project['pro_code']) ?>
                                            <?php if ($project['goal']): ?>
                                                • <?= esc(substr($project['goal'], 0, 50)) ?><?= strlen($project['goal']) > 50 ? '...' : '' ?>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <?php
                                $statusClasses = [
                                    'planning' => 'bg-secondary',
                                    'active' => 'bg-success',
                                    'on-hold' => 'bg-warning',
                                    'completed' => 'bg-primary',
                                    'cancelled' => 'bg-danger'
                                ];
                                $statusClass = $statusClasses[$project['status']] ?? 'bg-secondary';
                                ?>
                                <span class="badge <?= $statusClass ?> text-uppercase">
                                    <?= esc($project['status']) ?>
                                </span>
                            </td>
                            <td>
                                <div style="font-size: 0.875rem; color: var(--text-secondary);">
                                    <?php if ($project['country_name'] || $project['province_name'] || $project['district_name'] || $project['llg_name']): ?>
                                        <?php
                                        $locationParts = array_filter([
                                            $project['llg_name'],
                                            $project['district_name'],
                                            $project['province_name'],
                                            $project['country_name']
                                        ]);
                                        echo esc(implode(', ', $locationParts));
                                        ?>
                                    <?php else: ?>
                                        <span style="color: var(--text-muted);">No location set</span>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <div style="font-size: 0.875rem; color: var(--text-secondary);">
                                    <?php if ($project['start_date'] && $project['end_date']): ?>
                                        <div><?= date('M j, Y', strtotime($project['start_date'])) ?></div>
                                        <div style="font-size: 0.75rem; color: var(--text-muted);">to <?= date('M j, Y', strtotime($project['end_date'])) ?></div>
                                    <?php elseif ($project['start_date']): ?>
                                        <div>Started: <?= date('M j, Y', strtotime($project['start_date'])) ?></div>
                                    <?php elseif ($project['initiation_date']): ?>
                                        <div>Initiated: <?= date('M j, Y', strtotime($project['initiation_date'])) ?></div>
                                    <?php else: ?>
                                        <span style="color: var(--text-muted);">No timeline set</span>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <span style="color: var(--text-secondary); font-size: 0.875rem;">
                                    <?= date('M j, Y', strtotime($project['created_at'])) ?>
                                </span>
                                <?php if ($project['creator_name']): ?>
                                    <div style="font-size: 0.75rem; color: var(--text-muted);">
                                        by <?= esc($project['creator_name']) ?>
                                    </div>
                                <?php endif; ?>
                            </td>
                            <td class="text-center">
                                <div class="d-flex gap-2 justify-content-center">
                                    <a href="<?= base_url('admin/projects/' . $project['id']) ?>"
                                       class="btn btn-outline-primary btn-sm" title="View Details">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <a href="<?= base_url('admin/projects/' . $project['id'] . '/edit') ?>"
                                       class="btn btn-outline-secondary btn-sm" title="Edit Project">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    <button onclick="showDeleteModal(<?= $project['id'] ?>, '<?= esc($project['title']) ?>')"
                                            class="btn btn-outline-danger btn-sm" title="Delete Project">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <div class="card-body text-center py-5">
            <div class="display-1 text-muted opacity-50 mb-4">
                <i class="bi bi-kanban"></i>
            </div>
            <h5 class="text-muted mb-3">No projects found</h5>
            <p>Try adjusting your filters or create a new project.</p>
            <a href="<?= base_url('admin/projects/create') ?>" class="btn btn-primary" style="margin-top: var(--spacing-md);">
                Create First Project
            </a>
        </div>
    <?php endif; ?>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; align-items: center; justify-content: center;">
    <div style="background: white; border-radius: var(--radius-lg); padding: var(--spacing-xl); max-width: 400px; width: 90%;">
        <h3 style="color: var(--text-primary); margin-bottom: var(--spacing-lg);">Delete Project</h3>
        <p style="color: var(--text-secondary); margin-bottom: var(--spacing-lg);">
            Are you sure you want to delete the project <strong id="deleteProjectName"></strong>?
        </p>
        <p style="color: var(--brand-danger); font-size: 0.875rem; margin-bottom: var(--spacing-lg);">
            This action cannot be undone. All project data will be permanently removed.
        </p>
        
        <form id="deleteForm" method="post" style="display: none;">
            <?= csrf_field() ?>
        </form>
        
        <div class="d-flex gap-md justify-content-between">
            <button onclick="hideDeleteModal()" class="btn btn-secondary">Cancel</button>
            <button onclick="confirmDelete()" class="btn btn-danger">Delete Project</button>
        </div>
    </div>
</div>

<script>
let currentDeleteProjectId = null;

function showDeleteModal(projectId, projectName) {
    currentDeleteProjectId = projectId;
    document.getElementById('deleteProjectName').textContent = projectName;
    document.getElementById('deleteModal').style.display = 'flex';
}

function hideDeleteModal() {
    document.getElementById('deleteModal').style.display = 'none';
    currentDeleteProjectId = null;
}

function confirmDelete() {
    if (currentDeleteProjectId) {
        const form = document.getElementById('deleteForm');
        form.action = '<?= base_url('admin/projects/') ?>' + currentDeleteProjectId + '/delete';
        form.submit();
    }
}

// Close modal when clicking outside
document.getElementById('deleteModal').addEventListener('click', function(e) {
    if (e.target === this) {
        hideDeleteModal();
    }
});
</script>

<?= $this->endSection() ?>
